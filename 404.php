<?php
/**
 * 404错误页面模板 - 子比主题定制版
 * 现代化、精美的404页面设计
 */

get_header(); ?>

<style>
.error-404-container {
    min-height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    position: relative;
    overflow: hidden;
}

.error-404-content {
    max-width: 800px;
    text-align: center;
    background: white;
    border-radius: 20px;
    padding: 60px 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 10;
}

.error-404-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, var(--theme-color, #ff6b6b), #4ecdc4, #45b7d1);
    border-radius: 20px 20px 0 0;
}

.error-illustration {
    width: 200px;
    height: 200px;
    margin: 0 auto 40px;
    position: relative;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

.error-code {
    font-size: 6rem;
    font-weight: 900;
    background: linear-gradient(45deg, var(--theme-color, #ff6b6b), #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
    position: relative;
}

.error-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-color, #2c3e50);
    margin-bottom: 15px;
}

.error-message {
    font-size: 1.1rem;
    color: var(--text-muted, #7f8c8d);
    margin-bottom: 40px;
    line-height: 1.6;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.error-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 40px;
}

.error-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.error-btn-primary {
    background: linear-gradient(45deg, var(--theme-color, #ff6b6b), #4ecdc4);
    color: white;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
}

.error-btn-secondary {
    background: transparent;
    color: var(--text-color, #2c3e50);
    border: 2px solid var(--border-color, #e9ecef);
}

.error-btn:hover {
    transform: translateY(-3px);
}

.error-btn-primary:hover {
    box-shadow: 0 15px 40px rgba(255, 107, 107, 0.4);
}

.error-btn-secondary:hover {
    background: var(--theme-color, #2c3e50);
    color: white;
}

.error-search {
    max-width: 400px;
    margin: 0 auto 40px;
}

.search-form {
    display: flex;
    background: var(--bg-color, #f8f9fa);
    border-radius: 50px;
    padding: 5px;
    border: 2px solid var(--border-color, #e9ecef);
    transition: all 0.3s ease;
}

.search-form:focus-within {
    border-color: var(--theme-color, #4ecdc4);
    box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 15px 20px;
    font-size: 1rem;
    outline: none;
    color: var(--text-color, #2c3e50);
}

.search-submit {
    background: linear-gradient(45deg, var(--theme-color, #ff6b6b), #4ecdc4);
    border: none;
    border-radius: 50px;
    padding: 15px 25px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-submit:hover {
    transform: scale(1.05);
}

.helpful-links {
    padding-top: 30px;
    border-top: 1px solid var(--border-color, #e9ecef);
}

.helpful-links h3 {
    color: var(--text-color, #2c3e50);
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    max-width: 600px;
    margin: 0 auto;
}

.link-item {
    padding: 15px;
    background: var(--bg-light, #f8f9fa);
    border-radius: 10px;
    text-decoration: none;
    color: var(--text-color, #2c3e50);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    text-align: center;
}

.link-item:hover {
    border-color: var(--theme-color, #4ecdc4);
    transform: translateY(-2px);
    color: var(--text-color, #2c3e50);
}

.link-icon {
    font-size: 1.5rem;
    margin-bottom: 5px;
    display: block;
}

/* 背景装饰 */
.bg-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(78, 205, 196, 0.1);
    animation: float 6s ease-in-out infinite;
}

.decoration-circle:nth-child(1) {
    width: 100px;
    height: 100px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.decoration-circle:nth-child(2) {
    width: 60px;
    height: 60px;
    top: 20%;
    right: 15%;
    animation-delay: 2s;
}

.decoration-circle:nth-child(3) {
    width: 80px;
    height: 80px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .error-404-content {
        padding: 40px 20px;
        margin: 10px;
    }
    
    .error-code {
        font-size: 4rem;
    }
    
    .error-title {
        font-size: 2rem;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .error-btn {
        width: 200px;
        justify-content: center;
    }
    
    .links-grid {
        grid-template-columns: 1fr;
    }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    .error-404-container {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }
    
    .error-404-content {
        background: var(--bg-dark, #34495e);
        color: var(--text-light, #ecf0f1);
    }
}
</style>

<div class="error-404-container">
    <div class="bg-decoration">
        <div class="decoration-circle"></div>
        <div class="decoration-circle"></div>
        <div class="decoration-circle"></div>
    </div>
    
    <div class="error-404-content">
        <div class="error-illustration">
            <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                <!-- 简化的404插画 -->
                <circle cx="100" cy="100" r="80" fill="none" stroke="var(--theme-color, #4ecdc4)" stroke-width="3" opacity="0.3"/>
                <text x="100" y="110" text-anchor="middle" font-size="24" font-weight="bold" fill="var(--theme-color, #4ecdc4)">404</text>
                <path d="M 60 140 Q 100 160 140 140" stroke="var(--theme-color, #4ecdc4)" stroke-width="3" fill="none" opacity="0.6"/>
            </svg>
        </div>
        
        <div class="error-code">404</div>
        <h1 class="error-title">页面未找到</h1>
        <p class="error-message">
            抱歉，您访问的页面可能已被删除、重命名或暂时不可用。<br>
            让我们帮您找到正确的内容。
        </p>

        <div class="error-search">
            <form class="search-form" role="search" method="get" action="<?php echo esc_url(home_url('/')); ?>">
                <input type="search" class="search-input" placeholder="搜索您想要的内容..." value="<?php echo get_search_query(); ?>" name="s">
                <button type="submit" class="search-submit">
                    <i class="fa fa-search"></i>
                </button>
            </form>
        </div>

        <div class="error-actions">
            <a href="<?php echo esc_url(home_url('/')); ?>" class="error-btn error-btn-primary">
                <i class="fa fa-home"></i>
                返回首页
            </a>
            <a href="javascript:history.back()" class="error-btn error-btn-secondary">
                <i class="fa fa-arrow-left"></i>
                返回上页
            </a>
        </div>

        <div class="helpful-links">
            <h3>您可能感兴趣的内容：</h3>
            <div class="links-grid">
                <a href="<?php echo esc_url(home_url('/')); ?>" class="link-item">
                    <span class="link-icon">🏠</span>
                    首页
                </a>
                <?php if (function_exists('zib_get_option') && zib_get_option('about_page')) : ?>
                <a href="<?php echo esc_url(get_permalink(zib_get_option('about_page'))); ?>" class="link-item">
                    <span class="link-icon">ℹ️</span>
                    关于我们
                </a>
                <?php endif; ?>
                <a href="<?php echo esc_url(home_url('/contact')); ?>" class="link-item">
                    <span class="link-icon">📧</span>
                    联系我们
                </a>
                <a href="<?php echo esc_url(home_url('/archives')); ?>" class="link-item">
                    <span class="link-icon">📝</span>
                    文章归档
                </a>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 搜索表单增强
    const searchForm = document.querySelector('.search-form');
    const searchInput = document.querySelector('.search-input');
    
    searchInput.addEventListener('focus', function() {
        searchForm.style.transform = 'scale(1.02)';
    });
    
    searchInput.addEventListener('blur', function() {
        searchForm.style.transform = 'scale(1)';
    });
    
    // 添加键盘快捷键支持
    document.addEventListener('keydown', function(e) {
        if (e.key === '/' && !e.ctrlKey && !e.metaKey) {
            e.preventDefault();
            searchInput.focus();
        }
    });
});
</script>

<?php get_footer(); ?>
