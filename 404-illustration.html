<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面未找到 - 404</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            text-align: center;
            background: white;
            border-radius: 20px;
            padding: 60px 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
        }

        /* SVG插画 */
        .illustration {
            width: 300px;
            height: 300px;
            margin: 0 auto 40px;
            position: relative;
        }

        .astronaut {
            width: 100%;
            height: 100%;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .stars {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .star {
            position: absolute;
            background: #ffd700;
            border-radius: 50%;
            animation: twinkle 2s ease-in-out infinite;
        }

        .star:nth-child(1) { width: 4px; height: 4px; top: 20%; left: 10%; animation-delay: 0s; }
        .star:nth-child(2) { width: 3px; height: 3px; top: 30%; left: 80%; animation-delay: 0.5s; }
        .star:nth-child(3) { width: 5px; height: 5px; top: 60%; left: 20%; animation-delay: 1s; }
        .star:nth-child(4) { width: 3px; height: 3px; top: 70%; left: 90%; animation-delay: 1.5s; }
        .star:nth-child(5) { width: 4px; height: 4px; top: 10%; left: 70%; animation-delay: 2s; }

        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        .error-code {
            font-size: 6rem;
            font-weight: 900;
            color: #2c3e50;
            margin-bottom: 20px;
            position: relative;
        }

        .error-code::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
            border-radius: 2px;
        }

        .error-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .error-message {
            font-size: 1.1rem;
            color: #7f8c8d;
            margin-bottom: 40px;
            line-height: 1.6;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }

        .action-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: #2c3e50;
            border: 2px solid #2c3e50;
        }

        .btn:hover {
            transform: translateY(-3px);
        }

        .btn-primary:hover {
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.4);
        }

        .btn-secondary:hover {
            background: #2c3e50;
            color: white;
        }

        /* 有用链接 */
        .helpful-links {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #ecf0f1;
        }

        .helpful-links h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.2rem;
        }

        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            max-width: 600px;
            margin: 0 auto;
        }

        .link-item {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            text-decoration: none;
            color: #2c3e50;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .link-item:hover {
            background: #e9ecef;
            border-color: #4ecdc4;
            transform: translateY(-2px);
        }

        .link-icon {
            font-size: 1.5rem;
            margin-bottom: 5px;
            display: block;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 40px 20px;
                margin: 10px;
            }
            
            .illustration {
                width: 250px;
                height: 250px;
            }
            
            .error-code {
                font-size: 4rem;
            }
            
            .error-title {
                font-size: 2rem;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 200px;
            }
            
            .links-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="illustration">
            <div class="stars">
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
            </div>
            <svg class="astronaut" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                <!-- 宇航员插画 -->
                <defs>
                    <linearGradient id="spaceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#667eea"/>
                        <stop offset="100%" style="stop-color:#764ba2"/>
                    </linearGradient>
                </defs>
                
                <!-- 头盔 -->
                <circle cx="100" cy="80" r="35" fill="url(#spaceGradient)" opacity="0.8"/>
                <circle cx="100" cy="80" r="30" fill="#f8f9fa" stroke="#2c3e50" stroke-width="2"/>
                
                <!-- 脸部 -->
                <circle cx="100" cy="80" r="25" fill="#ffeaa7"/>
                <circle cx="92" cy="75" r="2" fill="#2c3e50"/>
                <circle cx="108" cy="75" r="2" fill="#2c3e50"/>
                <path d="M 95 85 Q 100 90 105 85" stroke="#2c3e50" stroke-width="2" fill="none"/>
                
                <!-- 身体 -->
                <ellipse cx="100" cy="130" rx="25" ry="35" fill="#74b9ff"/>
                <rect x="85" y="110" width="30" height="40" rx="15" fill="#0984e3"/>
                
                <!-- 手臂 -->
                <ellipse cx="70" cy="125" rx="8" ry="20" fill="#74b9ff" transform="rotate(-20 70 125)"/>
                <ellipse cx="130" cy="125" rx="8" ry="20" fill="#74b9ff" transform="rotate(20 130 125)"/>
                
                <!-- 腿部 -->
                <ellipse cx="90" cy="170" rx="8" ry="25" fill="#74b9ff"/>
                <ellipse cx="110" cy="170" rx="8" ry="25" fill="#74b9ff"/>
                
                <!-- 装饰 -->
                <circle cx="100" cy="120" r="3" fill="#ff7675"/>
                <rect x="95" y="135" width="10" height="5" rx="2" fill="#00b894"/>
            </svg>
        </div>

        <div class="error-code">404</div>
        <h1 class="error-title">哎呀！页面飞到太空了</h1>
        <p class="error-message">
            看起来我们的宇航员在太空中迷路了，就像您要找的页面一样。
            不过别担心，我们会帮您安全返回地球！
        </p>

        <div class="action-buttons">
            <a href="/" class="btn btn-primary">🚀 返回地球</a>
            <a href="javascript:history.back()" class="btn btn-secondary">⬅️ 返回上页</a>
        </div>

        <div class="helpful-links">
            <h3>或者您可以访问这些地方：</h3>
            <div class="links-grid">
                <a href="/" class="link-item">
                    <span class="link-icon">🏠</span>
                    首页
                </a>
                <a href="/about" class="link-item">
                    <span class="link-icon">ℹ️</span>
                    关于我们
                </a>
                <a href="/contact" class="link-item">
                    <span class="link-icon">📧</span>
                    联系我们
                </a>
                <a href="/blog" class="link-item">
                    <span class="link-icon">📝</span>
                    博客
                </a>
            </div>
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 鼠标移动视差效果
            document.addEventListener('mousemove', function(e) {
                const astronaut = document.querySelector('.astronaut');
                const x = (e.clientX / window.innerWidth) * 10 - 5;
                const y = (e.clientY / window.innerHeight) * 10 - 5;
                
                astronaut.style.transform = `translate(${x}px, ${y}px)`;
            });

            // 点击宇航员的彩蛋
            document.querySelector('.astronaut').addEventListener('click', function() {
                this.style.animation = 'float 0.5s ease-in-out';
                setTimeout(() => {
                    this.style.animation = 'float 3s ease-in-out infinite';
                }, 500);
            });
        });
    </script>
</body>
</html>
